from PIL import Image
import numpy as np
import cv2

# Kép betöltése
image_path = "pictures\\kotcse_tisza.jpg"
img = Image.open(image_path)

# Átalakítás OpenCV formátumba
img_cv = cv2.cvtColor(np.array(img), cv2.COLOR_RGB2BGR)

# Szürkeárnyalatos kép
gray = cv2.cvtColor(img_cv, cv2.COLOR_BGR2GRAY)

# Zajcsökkentés
blurred = cv2.<PERSON><PERSON><PERSON><PERSON>lur(gray, (11, 11), 0)

# Adaptív küszöbölés a tömeg kiemelésére
thresh = cv2.adaptiveThreshold(blurred, 255, cv2.ADAPTIVE_THRESH_MEAN_C,
                               cv2.THRESH_BINARY_INV, 21, 5)

# Kontúrok keresése
contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

# Ko<PERSON><PERSON><PERSON> szűrése méret al<PERSON> (kis zaj kis<PERSON>)
min_area = 5
filtered_contours = [c for c in contours if cv2.contourArea(c) > min_area]

estimated_people = len(filtered_contours)

print(f"Approximate number of people: {estimated_people}")

# Másolat a képről a jelölésekhez
output_img = img_cv.copy()

# Kontúrok kirajzolása
cv2.drawContours(output_img, filtered_contours, -1, (0, 255, 0), 1)

# OpenCV -> PIL átalakítás megjelenítéshez
output_pil = Image.fromarray(cv2.cvtColor(output_img, cv2.COLOR_BGR2RGB))
saved_path = "pictures\\kotcse_tisza_output.jpg"
output_pil.save(saved_path)

# Maszk létrehozása csak a tömeg területére (ROI - régió a nagy csoport körül)
mask = np.zeros(gray.shape, dtype=np.uint8)

# Tömeg környékének kézi kiválasztása (koordináták kb. a kép közepén)
# Poligon a nagyobb tömeg körül (durva körvonal a fő téren)
poly_points = np.array([
    [600, 950], [850, 700], [1250, 650],
    [1500, 850], [1500, 1200], [1100, 1350],
    [700, 1200]
])

cv2.fillPoly(mask, [poly_points], 255)

# Maszkolás
masked_thresh = cv2.bitwise_and(thresh, thresh, mask=mask)

# Új kontúrok keresése
contours_roi, _ = cv2.findContours(masked_thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

# Szűrés méret alapján
filtered_contours_roi = [c for c in contours_roi if 5 < cv2.contourArea(c) < 200]

# Emberszám újra becsülve
estimated_people_roi = len(filtered_contours_roi)

# Kirajzolás
output_img_roi = img_cv.copy()
cv2.drawContours(output_img_roi, filtered_contours_roi, -1, (255, 0, 0), 1)

output_pil_roi = Image.fromarray(cv2.cvtColor(output_img_roi, cv2.COLOR_BGR2RGB))

print(estimated_people_roi, "people in the main crowd.")
saved_path = "pictures\\kotcse_tisza_output_roi.jpg"
output_pil_roi.save(saved_path)



