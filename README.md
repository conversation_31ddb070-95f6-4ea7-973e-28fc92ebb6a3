# Fejlett Emberszámlálási Rendszer

Ez a projekt különböző számítógépes látás technikákat implementál emberek számlálására képeken és videókban.

## 🚀 Gyors kezdés

### 1. Telepítés

```bash
# Alapvető csomagok telepítése
pip install -r requirements.txt

# Vagy egyenként:
pip install opencv-python pillow numpy ultralytics scipy scikit-learn matplotlib
```

### 2. <PERSON><PERSON><PERSON><PERSON>

```bash
# Összes módszer összehasonlítása
python compare_methods.py

# Csak YOLO használata
python yolo_people_counter.py

# Sűrűségbecslés használata
python density_estimation_counter.py
```

## 📊 Módszerek összehasonlítása

| Módszer | Pontosság | Sebesség | Használati terület |
|---------|-----------|----------|-------------------|
| **YOLO v8** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | Általános célú, legjobb pontosság |
| **Density Estimation** | ⭐⭐⭐⭐ | ⭐⭐ | Nagy tömegek, fesztiválok |
| **Background Subtraction** | ⭐⭐⭐ | ⭐⭐⭐⭐ | Videók, mozgó objektumok |
| **Eredeti kontúr** | ⭐⭐ | ⭐⭐⭐⭐⭐ | Gyors prototípusok |

## 🎯 Módszerek részletesen

### 1. YOLO v8 Objektumdetektálás
- **Előnyök**: Nagyon pontos, előre betanított, robusztus
- **Hátrányok**: Lassabb, nagy modell méret
- **Ajánlott**: Általános használatra, amikor pontosság a legfontosabb

```python
from yolo_people_counter import YOLOPeopleCounter

counter = YOLOPeopleCounter()
people_count, result_img, detections = counter.count_people("kep.jpg")
print(f"Detektált emberek: {people_count}")
```

### 2. Sűrűségbecslés (Density Estimation)
- **Előnyök**: Jó nagy tömegekhez, heatmap vizualizáció
- **Hátrányok**: Komplex paraméterezés
- **Ajánlott**: Fesztiválok, nagy rendezvények elemzésére

```python
from density_estimation_counter import DensityEstimationCounter

counter = DensityEstimationCounter()
estimated_people, density_map, result_img = counter.count_people_density_map("kep.jpg")
```

### 3. Háttérkivonás (Background Subtraction)
- **Előnyök**: Jó mozgó objektumokhoz
- **Hátrányok**: Referencia kép szükséges
- **Ajánlott**: Videó elemzéshez, biztonsági kamerákhoz

```python
from background_subtraction_counter import BackgroundSubtractionCounter

counter = BackgroundSubtractionCounter()
people_count, result_img = counter.count_people_static_improved("kep.jpg")
```

## 🔧 Konfigurációs lehetőségek

### YOLO beállítások
```python
# Bizonyossági küszöb állítása
counter.count_people("kep.jpg", confidence_threshold=0.5)

# ROI (Region of Interest) használata
roi_polygon = [(x1,y1), (x2,y2), (x3,y3), ...]
counter.count_people_with_roi("kep.jpg", roi_polygon)
```

### Density Estimation beállítások
```python
# Sűrűségi küszöb állítása
counter.count_people_density_map("kep.jpg", density_threshold=0.3)
```

## 📈 Teljesítmény optimalizálás

### GPU gyorsítás (YOLO-hoz)
```bash
# CUDA támogatással
pip install torch torchvision --index-url https://download.pytorch.org/whl/cu118
```

### Memória optimalizálás
- Nagyobb képeknél használj kisebb felbontást
- Batch feldolgozás több képhez
- ROI használata a releváns területek kijelölésére

## 🧪 Tesztelés

```bash
# Tesztek futtatása
python -m pytest tests/

# Egyedi teszt
python test_people_counter.py
```

## 📁 Projekt struktúra

```
PersonNumber/
├── main.py                          # Eredeti implementáció
├── yolo_people_counter.py           # YOLO-alapú megoldás
├── density_estimation_counter.py    # Sűrűségbecslés
├── background_subtraction_counter.py # Háttérkivonás
├── compare_methods.py               # Módszerek összehasonlítása
├── requirements.txt                 # Függőségek
├── README.md                       # Ez a fájl
└── pictures/                       # Képek és eredmények
    ├── kotcse_tisza.jpg
    ├── yolo_result_full.jpg
    ├── density_result.jpg
    └── method_comparison.png
```

## 🎨 Eredmények vizualizálása

A rendszer automatikusan generál:
- Annotált képeket a detektálásokkal
- Sűrűségtérképeket
- Összehasonlító diagramokat
- Heatmap elemzéseket

## ⚡ Gyors tippek

1. **Legjobb pontossághoz**: Használd a YOLO v8 módszert
2. **Gyors prototípusokhoz**: Maradj az eredeti kontúr módszernél
3. **Nagy tömegekhez**: Próbáld ki a density estimation-t
4. **Videókhoz**: Background subtraction a legjobb választás

## 🐛 Hibaelhárítás

### Gyakori problémák:

**YOLO modell nem töltődik le:**
```bash
# Manuális letöltés
python -c "from ultralytics import YOLO; YOLO('yolov8n.pt')"
```

**OpenCV hibák:**
```bash
pip uninstall opencv-python
pip install opencv-python-headless
```

**Memória problémák:**
- Csökkentsd a kép felbontását
- Használj kisebb YOLO modellt (yolov8n.pt helyett yolov8s.pt)

## 📞 Támogatás

Ha problémába ütközöl:
1. Ellenőrizd a requirements.txt-ben lévő verziószámokat
2. Győződj meg róla, hogy a kép útvonala helyes
3. Próbáld ki a compare_methods.py-t a teljes összehasonlításhoz

## 🔮 Jövőbeli fejlesztések

- [ ] Real-time videó feldolgozás
- [ ] Webes felület
- [ ] Mobil alkalmazás
- [ ] Több kamera egyidejű feldolgozása
- [ ] Crowd flow analysis
- [ ] Hőtérkép alapú detektálás
