import cv2
import numpy as np
from ultralytics import YOLO
from PIL import Image

class YOLOPeopleCounter:
    def __init__(self, model_path='yolov8n.pt'):
        """
        YOLO-alapú emberszámláló inicializálása
        model_path: YOLOv8 modell útvonala (automatikusan letöltődik ha nincs meg)
        """
        self.model = YOLO(model_path)
        
    def count_people(self, image_path, confidence_threshold=0.5):
        """
        Emberek számlálása egy képen YOLO segítségével
        
        Args:
            image_path: A kép útvonala
            confidence_threshold: Minim<PERSON>lis bizonyossági kü<PERSON>ö<PERSON> (0.0-1.0)
            
        Returns:
            tuple: (emberek_sz<PERSON>ma, eredmény_kép, detektálások)
        """
        # Kép betöltése
        img = cv2.imread(image_path)
        if img is None:
            raise ValueError(f"Nem sikerült betölteni a képet: {image_path}")
            
        # YOLO detektálás futtatása
        results = self.model(img, conf=confidence_threshold)
        
        # Emberek (class_id = 0 a COCO adathalmazban) szűrése
        people_detections = []
        people_count = 0
        
        for result in results:
            boxes = result.boxes
            if boxes is not None:
                for box in boxes:
                    # Class ID ellenőrzése (0 = person)
                    if int(box.cls[0]) == 0:  # person class
                        people_count += 1
                        
                        # Bounding box koordináták
                        x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                        confidence = box.conf[0].cpu().numpy()
                        
                        people_detections.append({
                            'bbox': (int(x1), int(y1), int(x2), int(y2)),
                            'confidence': float(confidence)
                        })
                        
                        # Téglalap rajzolása az emberre
                        cv2.rectangle(img, (int(x1), int(y1)), (int(x2), int(y2)), (0, 255, 0), 2)
                        
                        # Bizonyossági érték kiírása
                        label = f'Person: {confidence:.2f}'
                        cv2.putText(img, label, (int(x1), int(y1)-10), 
                                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
        
        return people_count, img, people_detections
    
    def count_people_with_roi(self, image_path, roi_polygon, confidence_threshold=0.5):
        """
        Emberek számlálása egy megadott területen (ROI - Region of Interest)
        
        Args:
            image_path: A kép útvonala
            roi_polygon: ROI poligon pontjai [(x1,y1), (x2,y2), ...]
            confidence_threshold: Minimális bizonyossági küszöb
            
        Returns:
            tuple: (emberek_száma_roi-ban, eredmény_kép, detektálások)
        """
        people_count, img, detections = self.count_people(image_path, confidence_threshold)
        
        # ROI maszk létrehozása
        mask = np.zeros(img.shape[:2], dtype=np.uint8)
        roi_points = np.array(roi_polygon, dtype=np.int32)
        cv2.fillPoly(mask, [roi_points], 255)
        
        # ROI kirajzolása
        cv2.polylines(img, [roi_points], True, (255, 0, 0), 3)
        
        # Csak a ROI-ban lévő emberek számlálása
        people_in_roi = 0
        roi_detections = []
        
        for detection in detections:
            x1, y1, x2, y2 = detection['bbox']
            center_x = (x1 + x2) // 2
            center_y = (y1 + y2) // 2
            
            # Ellenőrizzük, hogy a középpont a ROI-ban van-e
            if mask[center_y, center_x] > 0:
                people_in_roi += 1
                roi_detections.append(detection)
                
                # Kék színnel jelöljük a ROI-ban lévő embereket
                cv2.rectangle(img, (x1, y1), (x2, y2), (255, 0, 0), 3)
        
        return people_in_roi, img, roi_detections

def main():
    # YOLO emberszámláló inicializálása
    counter = YOLOPeopleCounter()
    
    # Kép útvonala
    image_path = "pictures/kotcse_tisza.jpg"
    
    try:
        # 1. Teljes kép elemzése
        print("YOLO-alapú emberszámlálás...")
        people_count, result_img, detections = counter.count_people(image_path, confidence_threshold=0.3)
        
        print(f"Detektált emberek száma: {people_count}")
        
        # Eredmény mentése
        cv2.imwrite("pictures/yolo_result_full.jpg", result_img)
        print("Eredmény mentve: pictures/yolo_result_full.jpg")
        
        # 2. ROI-alapú elemzés (ugyanaz a poligon mint az eredeti kódban)
        roi_polygon = [
            (600, 950), (850, 700), (1250, 650),
            (1500, 850), (1500, 1200), (1100, 1350),
            (700, 1200)
        ]
        
        people_in_roi, roi_result_img, roi_detections = counter.count_people_with_roi(
            image_path, roi_polygon, confidence_threshold=0.3
        )
        
        print(f"Emberek száma a kijelölt területen: {people_in_roi}")
        
        # ROI eredmény mentése
        cv2.imwrite("pictures/yolo_result_roi.jpg", roi_result_img)
        print("ROI eredmény mentve: pictures/yolo_result_roi.jpg")
        
        # Részletes statisztikák
        print(f"\nRészletes eredmények:")
        print(f"- Teljes kép: {people_count} ember")
        print(f"- Kijelölt terület: {people_in_roi} ember")
        print(f"- Átlagos bizonyosság: {np.mean([d['confidence'] for d in detections]):.2f}")
        
    except Exception as e:
        print(f"Hiba történt: {e}")
        print("Győződj meg róla, hogy telepítve van az ultralytics csomag:")
        print("pip install ultralytics")

if __name__ == "__main__":
    main()
