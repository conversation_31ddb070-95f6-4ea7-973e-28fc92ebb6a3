import cv2
import numpy as np
from scipy import ndimage
from sklearn.cluster import DBSCAN
import matplotlib.pyplot as plt
from PIL import Image

class DensityEstimationCounter:
    def __init__(self):
        """
        Sűrűségbecslés-alapú emberszámláló inicializálása
        Nagy tömegek elemzésére optimalizált
        """
        pass
    
    def count_people_density_map(self, image_path, density_threshold=0.3):
        """
        Emberek számlálása sűrűségtérkép alapján
        
        Args:
            image_path: Kép útvonala
            density_threshold: Sűrűségi küszöb (0.0-1.0)
            
        Returns:
            tuple: (becsült_emberszám, sűrűségtérkép, eredmény_kép)
        """
        # Kép betöltése
        img = cv2.imread(image_path)
        if img is None:
            raise ValueError(f"Nem sikerült betölteni a képet: {image_path}")
        
        # Szürkeárnyalatos konverzió
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        
        # Előfeldolgozás
        # 1. Zajcsökkentés
        blurred = cv2.GaussianBlur(gray, (5, 5), 0)
        
        # 2. Kontraszt javítás
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
        enhanced = clahe.apply(blurred)
        
        # 3. Edge detection kombinált megközelítéssel
        edges_canny = cv2.Canny(enhanced, 50, 150)
        edges_sobel = cv2.Sobel(enhanced, cv2.CV_64F, 1, 1, ksize=3)
        edges_sobel = np.uint8(np.absolute(edges_sobel))
        
        # Kombinált edge térkép
        combined_edges = cv2.bitwise_or(edges_canny, edges_sobel)
        
        # 4. Morfológiai műveletek
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
        processed = cv2.morphologyEx(combined_edges, cv2.MORPH_CLOSE, kernel)
        
        # 5. Sűrűségtérkép létrehozása Gaussian szűrővel
        density_map = cv2.GaussianBlur(processed.astype(np.float32), (15, 15), 0)
        
        # Normalizálás 0-1 tartományba
        density_map = density_map / np.max(density_map) if np.max(density_map) > 0 else density_map
        
        # Küszöbölés
        high_density_areas = density_map > density_threshold
        
        # Klaszterezés a magas sűrűségű területeken
        high_density_points = np.column_stack(np.where(high_density_areas))
        
        if len(high_density_points) > 0:
            # DBSCAN klaszterezés
            clustering = DBSCAN(eps=20, min_samples=10).fit(high_density_points)
            n_clusters = len(set(clustering.labels_)) - (1 if -1 in clustering.labels_ else 0)
            
            # Emberszám becslése klaszterek alapján
            estimated_people = self._estimate_people_from_clusters(
                high_density_points, clustering.labels_, density_map
            )
        else:
            n_clusters = 0
            estimated_people = 0
        
        # Vizualizáció
        result_img = self._create_visualization(
            img, density_map, high_density_areas, estimated_people
        )
        
        return estimated_people, density_map, result_img
    
    def _estimate_people_from_clusters(self, points, labels, density_map):
        """
        Emberszám becslése klaszterek alapján
        """
        unique_labels = set(labels)
        if -1 in unique_labels:
            unique_labels.remove(-1)  # Zaj eltávolítása
        
        total_people = 0
        
        for label in unique_labels:
            cluster_points = points[labels == label]
            cluster_size = len(cluster_points)
            
            # Klaszter területének kiszámítása
            if len(cluster_points) > 0:
                min_row, min_col = np.min(cluster_points, axis=0)
                max_row, max_col = np.max(cluster_points, axis=0)
                area = (max_row - min_row) * (max_col - min_col)
                
                # Átlagos sűrűség a klaszterben
                cluster_density = np.mean([density_map[point[0], point[1]] for point in cluster_points])
                
                # Emberszám becslése a terület és sűrűség alapján
                # Empirikus formula: nagyobb terület és magasabb sűrűség = több ember
                people_in_cluster = max(1, int(area / 500 * cluster_density * 10))
                total_people += people_in_cluster
        
        return total_people
    
    def _create_visualization(self, original_img, density_map, high_density_areas, estimated_people):
        """
        Vizualizáció létrehozása
        """
        result_img = original_img.copy()
        
        # Sűrűségtérkép színezése
        density_colored = cv2.applyColorMap(
            (density_map * 255).astype(np.uint8), cv2.COLORMAP_JET
        )
        
        # Átlátszó overlay
        overlay = cv2.addWeighted(result_img, 0.7, density_colored, 0.3, 0)
        
        # Magas sűrűségű területek kiemelése
        high_density_mask = high_density_areas.astype(np.uint8) * 255
        contours, _ = cv2.findContours(high_density_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        cv2.drawContours(overlay, contours, -1, (0, 255, 0), 2)
        
        # Szöveg hozzáadása
        cv2.putText(overlay, f'Estimated People: {estimated_people}', (10, 30), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
        cv2.putText(overlay, 'Density-based Analysis', (10, 70), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        
        return overlay
    
    def count_people_watershed(self, image_path):
        """
        Watershed algoritmus alapú emberszámlálás
        Jól elkülönített objektumokhoz
        """
        # Kép betöltése
        img = cv2.imread(image_path)
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        
        # Előfeldolgozás
        blurred = cv2.GaussianBlur(gray, (11, 11), 0)
        
        # Küszöbölés
        _, thresh = cv2.threshold(blurred, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)
        
        # Zajcsökkentés
        kernel = np.ones((3,3), np.uint8)
        opening = cv2.morphologyEx(thresh, cv2.MORPH_OPEN, kernel, iterations=2)
        
        # Biztos háttér
        sure_bg = cv2.dilate(opening, kernel, iterations=3)
        
        # Biztos előtér
        dist_transform = cv2.distanceTransform(opening, cv2.DIST_L2, 5)
        _, sure_fg = cv2.threshold(dist_transform, 0.7*dist_transform.max(), 255, 0)
        
        # Bizonytalan terület
        sure_fg = np.uint8(sure_fg)
        unknown = cv2.subtract(sure_bg, sure_fg)
        
        # Marker labeling
        _, markers = cv2.connectedComponents(sure_fg)
        markers = markers + 1
        markers[unknown == 255] = 0
        
        # Watershed
        markers = cv2.watershed(img, markers)
        img[markers == -1] = [255, 0, 0]  # Határok piros színnel
        
        # Objektumok számlálása
        unique_markers = np.unique(markers)
        # Háttér (1) és határok (-1) kizárása
        object_count = len(unique_markers) - 2 if len(unique_markers) > 2 else 0
        
        # Szöveg hozzáadása
        cv2.putText(img, f'Watershed Objects: {object_count}', (10, 30), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
        
        return object_count, img
    
    def create_heatmap_analysis(self, image_path, output_path=None):
        """
        Részletes heatmap elemzés létrehozása
        """
        estimated_people, density_map, result_img = self.count_people_density_map(image_path)
        
        # Matplotlib heatmap létrehozása
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        
        # Eredeti kép
        original = cv2.imread(image_path)
        original_rgb = cv2.cvtColor(original, cv2.COLOR_BGR2RGB)
        axes[0, 0].imshow(original_rgb)
        axes[0, 0].set_title('Original Image')
        axes[0, 0].axis('off')
        
        # Sűrűségtérkép
        im1 = axes[0, 1].imshow(density_map, cmap='hot', interpolation='nearest')
        axes[0, 1].set_title('Density Map')
        axes[0, 1].axis('off')
        plt.colorbar(im1, ax=axes[0, 1])
        
        # Eredmény overlay
        result_rgb = cv2.cvtColor(result_img, cv2.COLOR_BGR2RGB)
        axes[1, 0].imshow(result_rgb)
        axes[1, 0].set_title(f'Result (Estimated: {estimated_people} people)')
        axes[1, 0].axis('off')
        
        # Hisztogram
        axes[1, 1].hist(density_map.flatten(), bins=50, alpha=0.7)
        axes[1, 1].set_title('Density Distribution')
        axes[1, 1].set_xlabel('Density Value')
        axes[1, 1].set_ylabel('Frequency')
        
        plt.tight_layout()
        
        if output_path:
            plt.savefig(output_path, dpi=300, bbox_inches='tight')
            print(f"Heatmap elemzés mentve: {output_path}")
        
        plt.show()
        
        return fig

def main():
    counter = DensityEstimationCounter()
    
    image_path = "pictures/kotcse_tisza.jpg"
    
    try:
        print("Sűrűségbecslés-alapú emberszámlálás...")
        
        # 1. Density map elemzés
        estimated_people, density_map, result_img = counter.count_people_density_map(image_path)
        print(f"Becsült emberszám (density): {estimated_people}")
        
        # Eredmény mentése
        cv2.imwrite("pictures/density_result.jpg", result_img)
        print("Density eredmény mentve: pictures/density_result.jpg")
        
        # 2. Watershed elemzés
        watershed_count, watershed_img = counter.count_people_watershed(image_path)
        print(f"Objektumok száma (watershed): {watershed_count}")
        
        cv2.imwrite("pictures/watershed_result.jpg", watershed_img)
        print("Watershed eredmény mentve: pictures/watershed_result.jpg")
        
        # 3. Részletes heatmap elemzés
        counter.create_heatmap_analysis(image_path, "pictures/heatmap_analysis.png")
        
    except Exception as e:
        print(f"Hiba történt: {e}")
        print("Győződj meg róla, hogy telepítve vannak a szükséges csomagok:")
        print("pip install opencv-python numpy scipy scikit-learn matplotlib pillow")

if __name__ == "__main__":
    main()
