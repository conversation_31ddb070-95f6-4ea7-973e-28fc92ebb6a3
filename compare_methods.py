import cv2
import numpy as np
import time
from PIL import Image
import matplotlib.pyplot as plt

# Importálás a létrehozott modulokból
try:
    from yolo_people_counter import YOLOPeopleCounter
    YOLO_AVAILABLE = True
except ImportError:
    YOLO_AVAILABLE = False
    print("YOLO nem elérhető. Telepítsd: pip install ultralytics")

try:
    from background_subtraction_counter import BackgroundSubtractionCounter
    BG_SUB_AVAILABLE = True
except ImportError:
    BG_SUB_AVAILABLE = False
    print("Background subtraction nem elérhető.")

try:
    from density_estimation_counter import DensityEstimationCounter
    DENSITY_AVAILABLE = True
except ImportError:
    DENSITY_AVAILABLE = False
    print("Density estimation nem elérhető.")

class MethodComparator:
    def __init__(self):
        """
        Különböző emberszámlálási módszerek összehasonlítása
        """
        self.results = {}
        
    def run_original_method(self, image_path):
        """
        Az eredeti kontúr-alapú módszer futtatása
        """
        start_time = time.time()
        
        # Eredeti kód logikája
        img = Image.open(image_path)
        img_cv = cv2.cvtColor(np.array(img), cv2.COLOR_RGB2BGR)
        gray = cv2.cvtColor(img_cv, cv2.COLOR_BGR2GRAY)
        blurred = cv2.GaussianBlur(gray, (11, 11), 0)
        thresh = cv2.adaptiveThreshold(blurred, 255, cv2.ADAPTIVE_THRESH_MEAN_C,
                                       cv2.THRESH_BINARY_INV, 21, 5)
        contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        min_area = 5
        filtered_contours = [c for c in contours if cv2.contourArea(c) > min_area]
        estimated_people = len(filtered_contours)
        
        # ROI elemzés
        mask = np.zeros(gray.shape, dtype=np.uint8)
        poly_points = np.array([
            [600, 950], [850, 700], [1250, 650],
            [1500, 850], [1500, 1200], [1100, 1350],
            [700, 1200]
        ])
        cv2.fillPoly(mask, [poly_points], 255)
        masked_thresh = cv2.bitwise_and(thresh, thresh, mask=mask)
        contours_roi, _ = cv2.findContours(masked_thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        filtered_contours_roi = [c for c in contours_roi if 5 < cv2.contourArea(c) < 200]
        estimated_people_roi = len(filtered_contours_roi)
        
        execution_time = time.time() - start_time
        
        return {
            'method': 'Original Contour',
            'full_image_count': estimated_people,
            'roi_count': estimated_people_roi,
            'execution_time': execution_time,
            'accuracy_estimate': 'Low',
            'pros': ['Gyors', 'Egyszerű implementáció', 'Nincs külső függőség'],
            'cons': ['Pontatlan', 'Zajérzékeny', 'Nem különbözteti meg az embereket']
        }
    
    def run_all_methods(self, image_path):
        """
        Összes elérhető módszer futtatása és összehasonlítása
        """
        print(f"Módszerek összehasonlítása a képen: {image_path}")
        print("=" * 60)
        
        results = []
        
        # 1. Eredeti módszer
        print("1. Eredeti kontúr-alapú módszer...")
        original_result = self.run_original_method(image_path)
        results.append(original_result)
        
        # 2. YOLO módszer
        if YOLO_AVAILABLE:
            print("2. YOLO objektumdetektálás...")
            try:
                start_time = time.time()
                yolo_counter = YOLOPeopleCounter()
                
                # ROI poligon
                roi_polygon = [
                    (600, 950), (850, 700), (1250, 650),
                    (1500, 850), (1500, 1200), (1100, 1350),
                    (700, 1200)
                ]
                
                full_count, _, _ = yolo_counter.count_people(image_path, confidence_threshold=0.3)
                roi_count, _, _ = yolo_counter.count_people_with_roi(image_path, roi_polygon, 0.3)
                execution_time = time.time() - start_time
                
                yolo_result = {
                    'method': 'YOLO v8',
                    'full_image_count': full_count,
                    'roi_count': roi_count,
                    'execution_time': execution_time,
                    'accuracy_estimate': 'Very High',
                    'pros': ['Nagyon pontos', 'Előre betanított', 'Robusztus'],
                    'cons': ['Lassabb', 'Nagy modell méret', 'GPU ajánlott']
                }
                results.append(yolo_result)
                
            except Exception as e:
                print(f"YOLO hiba: {e}")
        
        # 3. Background Subtraction módszer
        if BG_SUB_AVAILABLE:
            print("3. Háttérkivonás-alapú módszer...")
            try:
                start_time = time.time()
                bg_counter = BackgroundSubtractionCounter()
                bg_count, _ = bg_counter.count_people_static_improved(image_path)
                execution_time = time.time() - start_time
                
                bg_result = {
                    'method': 'Background Subtraction',
                    'full_image_count': bg_count,
                    'roi_count': 'N/A',
                    'execution_time': execution_time,
                    'accuracy_estimate': 'Medium',
                    'pros': ['Jó mozgó objektumokhoz', 'Közepes pontosság'],
                    'cons': ['Statikus képekhez korlátozott', 'Referencia kép szükséges']
                }
                results.append(bg_result)
                
            except Exception as e:
                print(f"Background Subtraction hiba: {e}")
        
        # 4. Density Estimation módszer
        if DENSITY_AVAILABLE:
            print("4. Sűrűségbecslés-alapú módszer...")
            try:
                start_time = time.time()
                density_counter = DensityEstimationCounter()
                density_count, _, _ = density_counter.count_people_density_map(image_path)
                watershed_count, _ = density_counter.count_people_watershed(image_path)
                execution_time = time.time() - start_time
                
                density_result = {
                    'method': 'Density Estimation',
                    'full_image_count': density_count,
                    'roi_count': f'Watershed: {watershed_count}',
                    'execution_time': execution_time,
                    'accuracy_estimate': 'Medium-High',
                    'pros': ['Jó nagy tömegekhez', 'Heatmap vizualizáció'],
                    'cons': ['Komplex paraméterezés', 'Számításigényes']
                }
                results.append(density_result)
                
            except Exception as e:
                print(f"Density Estimation hiba: {e}")
        
        return results
    
    def print_comparison_table(self, results):
        """
        Összehasonlító táblázat kiírása
        """
        print("\n" + "=" * 80)
        print("ÖSSZEHASONLÍTÓ EREDMÉNYEK")
        print("=" * 80)
        
        # Fejléc
        print(f"{'Módszer':<20} {'Teljes kép':<12} {'ROI/Spec.':<15} {'Idő (s)':<10} {'Pontosság':<12}")
        print("-" * 80)
        
        # Eredmények
        for result in results:
            method = result['method']
            full_count = result['full_image_count']
            roi_count = result['roi_count']
            exec_time = f"{result['execution_time']:.2f}"
            accuracy = result['accuracy_estimate']
            
            print(f"{method:<20} {full_count:<12} {roi_count:<15} {exec_time:<10} {accuracy:<12}")
        
        print("-" * 80)
        
        # Részletes elemzés
        print("\nRÉSZLETES ELEMZÉS:")
        print("=" * 40)
        
        for i, result in enumerate(results, 1):
            print(f"\n{i}. {result['method']}")
            print(f"   Előnyök: {', '.join(result['pros'])}")
            print(f"   Hátrányok: {', '.join(result['cons'])}")
    
    def create_visual_comparison(self, image_path, results, output_path="comparison_chart.png"):
        """
        Vizuális összehasonlítás létrehozása
        """
        methods = [r['method'] for r in results]
        counts = [r['full_image_count'] for r in results]
        times = [r['execution_time'] for r in results]
        
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # Emberszám összehasonlítás
        bars1 = ax1.bar(methods, counts, color=['red', 'green', 'blue', 'orange'][:len(methods)])
        ax1.set_title('Detektált emberek száma módszerenként')
        ax1.set_ylabel('Emberek száma')
        ax1.tick_params(axis='x', rotation=45)
        
        # Értékek kiírása a oszlopokra
        for bar, count in zip(bars1, counts):
            ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5, 
                    str(count), ha='center', va='bottom')
        
        # Végrehajtási idő összehasonlítás
        bars2 = ax2.bar(methods, times, color=['red', 'green', 'blue', 'orange'][:len(methods)])
        ax2.set_title('Végrehajtási idő módszerenként')
        ax2.set_ylabel('Idő (másodperc)')
        ax2.tick_params(axis='x', rotation=45)
        
        # Értékek kiírása a oszlopokra
        for bar, time_val in zip(bars2, times):
            ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01, 
                    f'{time_val:.2f}s', ha='center', va='bottom')
        
        plt.tight_layout()
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.show()
        
        print(f"Vizuális összehasonlítás mentve: {output_path}")

def main():
    comparator = MethodComparator()
    image_path = "pictures/kotcse_tisza.jpg"
    
    try:
        # Összes módszer futtatása
        results = comparator.run_all_methods(image_path)
        
        # Eredmények kiírása
        comparator.print_comparison_table(results)
        
        # Vizuális összehasonlítás
        comparator.create_visual_comparison(image_path, results, "pictures/method_comparison.png")
        
        print("\n" + "=" * 60)
        print("AJÁNLÁS:")
        print("=" * 60)
        print("1. LEGJOBB PONTOSSÁG: YOLO v8 - Modern deep learning alapú")
        print("2. LEGJOBB SEBESSÉG: Eredeti kontúr módszer")
        print("3. NAGY TÖMEGEKHEZ: Density Estimation")
        print("4. VIDEÓKHOZ: Background Subtraction")
        
    except Exception as e:
        print(f"Hiba történt: {e}")

if __name__ == "__main__":
    main()
