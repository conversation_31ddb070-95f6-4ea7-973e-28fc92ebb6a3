import cv2
import numpy as np
from PIL import Image

class BackgroundSubtractionCounter:
    def __init__(self):
        """
        Háttérkivonás-alapú emberszámláló inicializálása
        Főleg videókhoz és mozgó objektumokhoz használható
        """
        # Különböző háttérkivonási algoritmusok
        self.bg_subtractor_mog2 = cv2.createBackgroundSubtractorMOG2(detectShadows=True)
        self.bg_subtractor_knn = cv2.createBackgroundSubtractorKNN(detectShadows=True)
        
    def count_people_in_video(self, video_path, output_path=None):
        """
        Emberek számlálása videóban háttérkivonással
        
        Args:
            video_path: Videó fájl útvonala
            output_path: <PERSON><PERSON><PERSON> vide<PERSON>t<PERSON> (opcionális)
            
        Returns:
            dict: Statisztikák a detektálásról
        """
        cap = cv2.VideoCapture(video_path)
        
        if not cap.isOpened():
            raise ValueError(f"Nem sikerült megnyitni a videót: {video_path}")
        
        # Videó tulajdonságok
        fps = int(cap.get(cv2.CAP_PROP_FPS))
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        
        # Kimeneti videó writer (ha szükséges)
        out = None
        if output_path:
            fourcc = cv2.VideoWriter_fourcc(*'XVID')
            out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
        
        frame_count = 0
        people_counts = []
        
        print(f"Videó feldolgozása: {total_frames} frame, {fps} FPS")
        
        while True:
            ret, frame = cap.read()
            if not ret:
                break
                
            frame_count += 1
            
            # Háttérkivonás
            fg_mask = self.bg_subtractor_mog2.apply(frame)
            
            # Zajcsökkentés
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
            fg_mask = cv2.morphologyEx(fg_mask, cv2.MORPH_OPEN, kernel)
            
            # Kontúrok keresése
            contours, _ = cv2.findContours(fg_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            # Emberméretű objektumok szűrése
            people_contours = []
            for contour in contours:
                area = cv2.contourArea(contour)
                if 500 < area < 5000:  # Ember méretű területek
                    x, y, w, h = cv2.boundingRect(contour)
                    aspect_ratio = h / w
                    if 1.2 < aspect_ratio < 4.0:  # Ember alakú arány
                        people_contours.append(contour)
            
            people_count = len(people_contours)
            people_counts.append(people_count)
            
            # Vizualizáció
            result_frame = frame.copy()
            
            # Kontúrok rajzolása
            cv2.drawContours(result_frame, people_contours, -1, (0, 255, 0), 2)
            
            # Bounding boxok rajzolása
            for contour in people_contours:
                x, y, w, h = cv2.boundingRect(contour)
                cv2.rectangle(result_frame, (x, y), (x+w, y+h), (255, 0, 0), 2)
            
            # Szöveg hozzáadása
            cv2.putText(result_frame, f'People: {people_count}', (10, 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
            cv2.putText(result_frame, f'Frame: {frame_count}/{total_frames}', (10, 70), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
            
            # Kimeneti videóba írás
            if out:
                out.write(result_frame)
            
            # Előrehaladás kiírása
            if frame_count % 30 == 0:
                progress = (frame_count / total_frames) * 100
                print(f"Feldolgozva: {progress:.1f}%")
        
        # Erőforrások felszabadítása
        cap.release()
        if out:
            out.release()
        
        # Statisztikák
        stats = {
            'total_frames': total_frames,
            'average_people': np.mean(people_counts),
            'max_people': np.max(people_counts),
            'min_people': np.min(people_counts),
            'people_counts_per_frame': people_counts
        }
        
        return stats
    
    def count_people_static_improved(self, image_path, reference_image_path=None):
        """
        Javított statikus kép elemzés háttérkivonással
        Referencia háttérképpel dolgozik
        
        Args:
            image_path: Elemzendő kép útvonala
            reference_image_path: Háttér referencia kép (opcionális)
            
        Returns:
            tuple: (emberek_száma, eredmény_kép)
        """
        # Kép betöltése
        img = cv2.imread(image_path)
        if img is None:
            raise ValueError(f"Nem sikerült betölteni a képet: {image_path}")
        
        if reference_image_path:
            # Referencia háttér használata
            bg_img = cv2.imread(reference_image_path)
            if bg_img is None:
                print("Figyelmeztetés: Nem sikerült betölteni a referencia képet")
                return self._analyze_without_reference(img)
            
            # Háttérkivonás
            diff = cv2.absdiff(img, bg_img)
            gray_diff = cv2.cvtColor(diff, cv2.COLOR_BGR2GRAY)
            
            # Küszöbölés
            _, thresh = cv2.threshold(gray_diff, 30, 255, cv2.THRESH_BINARY)
            
        else:
            # Referencia nélküli elemzés
            return self._analyze_without_reference(img)
        
        # Zajcsökkentés
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
        thresh = cv2.morphologyEx(thresh, cv2.MORPH_OPEN, kernel)
        thresh = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)
        
        # Kontúrok keresése
        contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        # Emberméretű objektumok szűrése
        people_contours = []
        for contour in contours:
            area = cv2.contourArea(contour)
            if 200 < area < 3000:  # Ember méretű területek
                x, y, w, h = cv2.boundingRect(contour)
                aspect_ratio = h / w if w > 0 else 0
                if 1.0 < aspect_ratio < 3.5:  # Ember alakú arány
                    people_contours.append(contour)
        
        # Vizualizáció
        result_img = img.copy()
        cv2.drawContours(result_img, people_contours, -1, (0, 255, 0), 2)
        
        for contour in people_contours:
            x, y, w, h = cv2.boundingRect(contour)
            cv2.rectangle(result_img, (x, y), (x+w, y+h), (255, 0, 0), 2)
        
        people_count = len(people_contours)
        cv2.putText(result_img, f'People: {people_count}', (10, 30), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
        
        return people_count, result_img
    
    def _analyze_without_reference(self, img):
        """
        Referencia nélküli elemzés - edge detection alapú
        """
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        
        # Edge detection
        edges = cv2.Canny(gray, 50, 150)
        
        # Kontúrok keresése
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        # Emberméretű objektumok szűrése
        people_contours = []
        for contour in contours:
            area = cv2.contourArea(contour)
            if 300 < area < 2000:
                x, y, w, h = cv2.boundingRect(contour)
                aspect_ratio = h / w if w > 0 else 0
                if 1.5 < aspect_ratio < 4.0:
                    people_contours.append(contour)
        
        # Vizualizáció
        result_img = img.copy()
        cv2.drawContours(result_img, people_contours, -1, (0, 255, 0), 2)
        
        people_count = len(people_contours)
        cv2.putText(result_img, f'People (edge-based): {people_count}', (10, 30), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
        
        return people_count, result_img

def main():
    counter = BackgroundSubtractionCounter()
    
    # Statikus kép elemzése
    image_path = "pictures/kotcse_tisza.jpg"
    
    try:
        print("Háttérkivonás-alapú emberszámlálás (statikus kép)...")
        people_count, result_img = counter.count_people_static_improved(image_path)
        
        print(f"Detektált emberek száma: {people_count}")
        
        # Eredmény mentése
        cv2.imwrite("pictures/background_subtraction_result.jpg", result_img)
        print("Eredmény mentve: pictures/background_subtraction_result.jpg")
        
    except Exception as e:
        print(f"Hiba történt: {e}")

if __name__ == "__main__":
    main()
